import React, { useState } from 'react';
import {
    View,
    Text,
    TextInput,
    TouchableOpacity,
    Alert,
    Pressable,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import ModalDatePicker from 'react-native-modal-datetime-picker';
import * as DocumentPicker from 'expo-document-picker';
import { styles } from '../siteStyles';
import {
    FILE_CONSTRAINTS,
    validateStepData,
} from '../validations/siteValidations';
import DocumentPreviewModal from '../../Components/Profile/DocumentPreviewModal';

const EncumbranceStep = ({
    theme,
    formik,
    setStep,
    isSubmitting,
    onDocumentPreview,
}) => {
    const { values, errors, touched, setFieldValue, handleChange, handleBlur } =
        formik;

    const [previewModal, setPreviewModal] = useState({
        visible: false,
        url: '',
        type: '',
    });

    const [showDatePicker, setShowDatePicker] = useState(false);

    const handleNext = async () => {
        const stepValidation = await validateStepData('encumbrance', values);
        if (stepValidation.isValid) {
            setStep('propertyTax');
        } else {
            // Show first error
            const firstError = Object.values(stepValidation.errors)[0];
            if (firstError) {
                Alert.alert('Validation Error', firstError);
            }
        }
    };

    const handleBack = () => {
        setStep('location');
    };

    const previewDocument = (document, type) => {
        if (document && document.uri && onDocumentPreview) {
            onDocumentPreview(document.uri, type);
        }
    };

    const pickEncumbranceDocument = async () => {
        try {
            const res = await DocumentPicker.getDocumentAsync({
                type: FILE_CONSTRAINTS.ALLOWED_TYPES,
                multiple: false,
            });
            if (res.canceled) return;

            const asset = res.assets[0];
            if (asset.size > FILE_CONSTRAINTS.MAX_FILE_SIZE) {
                Alert.alert('File too large', 'Max 5 MB allowed.');
                return;
            }
            if (!FILE_CONSTRAINTS.ALLOWED_TYPES.includes(asset.mimeType)) {
                Alert.alert('Invalid type', 'Choose JPG, PNG, or PDF');
                return;
            }

            setFieldValue('encumbranceCert', asset);
        } catch (error) {
            Alert.alert('Error', 'Failed to select file');
        }
    };

    return (
        <>
            <Text style={[styles.sectionTitle, { color: theme.PRIMARY }]}>
                Encumbrance Certificate Details
            </Text>

            {/* Encumbrance Owner Name */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: errors.encOwnerName
                            ? theme.ERROR
                            : theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="person-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Owner name*"
                    value={values.encOwnerName}
                    onChangeText={handleChange('encOwnerName')}
                    onBlur={handleBlur('encOwnerName')}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>
            {errors.encOwnerName && touched.encOwnerName && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.encOwnerName}
                </Text>
            )}

            {/* Encumbrance Document Number */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: errors.encumbranceDocNumber
                            ? theme.ERROR
                            : theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="document-text-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Document number*"
                    value={values.encumbranceDocNumber}
                    onChangeText={handleChange('encumbranceDocNumber')}
                    onBlur={handleBlur('encumbranceDocNumber')}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>
            {errors.encumbranceDocNumber && touched.encumbranceDocNumber && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.encumbranceDocNumber}
                </Text>
            )}

            {/* Survey Number */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: errors.surveyNumber
                            ? theme.ERROR
                            : theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="map-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Survey number*"
                    value={values.surveyNumber}
                    onChangeText={handleChange('surveyNumber')}
                    onBlur={handleBlur('surveyNumber')}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>
            {errors.surveyNumber && touched.surveyNumber && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.surveyNumber}
                </Text>
            )}

            {/* Encumbrance Certificate */}
            <Text style={[styles.sectionTitle, { color: theme.PRIMARY }]}>
                Upload Certificate
            </Text>

            {/* Encumbrance Certificate Upload */}
            <TouchableOpacity
                style={[
                    styles.filePickerButton,
                    {
                        backgroundColor: values.encumbranceCert
                            ? theme.ACCENT
                            : theme.INPUT_BACKGROUND,
                        borderColor: values.encumbranceCert
                            ? theme.PRIMARY
                            : theme.INPUT_BORDER,
                    },
                ]}
                onPress={pickEncumbranceDocument}
            >
                <Ionicons
                    name={
                        values.encumbranceCert
                            ? 'checkmark-circle'
                            : 'document-attach-outline'
                    }
                    size={22}
                    color={
                        values.encumbranceCert
                            ? theme.PRIMARY
                            : theme.TEXT_SECONDARY
                    }
                    style={styles.inputIcon}
                />
                <Text
                    style={[
                        styles.filePickerText,
                        {
                            color: values.encumbranceCert
                                ? theme.PRIMARY
                                : theme.TEXT_SECONDARY,
                        },
                    ]}
                >
                    {values.encumbranceCert
                        ? `✓ ${values.encumbranceCert.name}`
                        : 'Encumbrance Certificate (JPG/PNG/PDF)*'}
                </Text>
            </TouchableOpacity>

            {/* Preview Button */}
            {values.encumbranceCert && (
                <TouchableOpacity
                    style={[
                        styles.previewButton,
                        {
                            backgroundColor: theme.ACCENT,
                            borderColor: theme.PRIMARY,
                        },
                    ]}
                    onPress={() =>
                        previewDocument(
                            values.encumbranceCert,
                            'Encumbrance Certificate'
                        )
                    }
                    activeOpacity={0.8}
                >
                    <Ionicons
                        name="eye-outline"
                        size={20}
                        color={theme.PRIMARY}
                        style={styles.inputIcon}
                    />
                    <Text
                        style={[
                            styles.previewButtonText,
                            { color: theme.PRIMARY },
                        ]}
                    >
                        Preview Document
                    </Text>
                </TouchableOpacity>
            )}

            {/* Navigation Buttons */}
            <View style={styles.buttonContainer}>
                <TouchableOpacity
                    style={[
                        styles.backButton,
                        { backgroundColor: theme.GRAY_LIGHT },
                    ]}
                    onPress={handleBack}
                    activeOpacity={0.8}
                >
                    <Text
                        style={[
                            styles.backButtonText,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Back
                    </Text>
                </TouchableOpacity>
                <TouchableOpacity
                    style={[styles.nextButton, { shadowColor: theme.PRIMARY }]}
                    onPress={handleNext}
                    activeOpacity={0.8}
                >
                    <LinearGradient
                        colors={[theme.PRIMARY, theme.SECONDARY]}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        style={styles.nextButtonGradient}
                    >
                        <Text
                            style={[
                                styles.nextButtonText,
                                { color: theme.WHITE },
                            ]}
                        >
                            Next
                        </Text>
                    </LinearGradient>
                </TouchableOpacity>
            </View>

            <DocumentPreviewModal
                visible={previewModal.visible}
                documentUrl={previewModal.url}
                documentType={previewModal.type}
                onClose={() =>
                    setPreviewModal({ visible: false, url: '', type: '' })
                }
            />
        </>
    );
};

export default EncumbranceStep;
