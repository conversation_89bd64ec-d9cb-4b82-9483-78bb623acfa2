import React, { useState, useEffect, useRef } from 'react';
import {
    View,
    Text,
    TextInput,
    TouchableOpacity,
    StyleSheet,
    Alert,
} from 'react-native';
import MapView, { Marker, PROVIDER_GOOGLE } from 'react-native-maps';
import * as Location from 'expo-location';
import { Ionicons } from '@expo/vector-icons';

const MapSelector = ({
    region,
    setRegion,
    marker,
    setMarker,
    fields,
    setFields,
    hasPermission,
    setHasPermission,
}) => {
    const [searchQuery, setSearchQuery] = useState('');
    const mapRef = useRef(null);

    useEffect(() => {
        (async () => {
            let { status } = await Location.requestForegroundPermissionsAsync();
            if (status !== 'granted') {
                console.error('Permission to access location was denied');

                System: Alert.alert(
                    'Permission Denied',
                    'Please allow location access to set your current location.'
                );
                setHasPermission(false);
                return;
            }
            setHasPermission(true);
            try {
                let currentLocation = await Location.getCurrentPositionAsync(
                    {}
                );
                const { latitude, longitude } = currentLocation.coords;
                setRegion({
                    latitude,
                    longitude,
                    latitudeDelta: 0.0922,
                    longitudeDelta: 0.0421,
                });
                setMarker({ latitude, longitude });
                setFields((f) => ({
                    ...f,
                    latitude: latitude.toFixed(6),
                    longitude: longitude.toFixed(6),
                }));
            } catch (error) {
                console.error('Error fetching location:', error);
                Alert.alert('Error', 'Failed to fetch current location.');
            }
        })();
    }, []);

    const handleMapPress = (event) => {
        const { latitude, longitude } = event.nativeEvent.coordinate;
        setMarker({ latitude, longitude });
        setFields((f) => ({
            ...f,
            latitude: latitude.toFixed(6),
            longitude: longitude.toFixed(6),
        }));
        setRegion((r) => ({
            ...r,
            latitude,
            longitude,
        }));
    };

    const handleCurrentLocation = async () => {
        if (!hasPermission) {
            Alert.alert('Permission Denied', 'Please allow location access.');
            return;
        }
        try {
            let currentLocation = await Location.getCurrentPositionAsync({});
            const { latitude, longitude } = currentLocation.coords;
            setRegion({
                latitude,
                longitude,
                latitudeDelta: 0.0922,
                longitudeDelta: 0.0421,
            });
            setMarker({ latitude, longitude });
            setFields((f) => ({
                ...f,
                latitude: latitude.toFixed(6),
                longitude: longitude.toFixed(6),
            }));
            if (mapRef.current) {
                mapRef.current.animateToRegion({
                    latitude,
                    longitude,
                    latitudeDelta: 0.0922,
                    longitudeDelta: 0.0922,
                });
            }
        } catch (error) {
            console.error('Error fetching current location:', error);
            Alert.alert('Error', 'Failed to fetch current location.');
        }
    };

    const handleSearch = async () => {
        if (!searchQuery.trim()) {
            Alert.alert('Error', 'Please enter a search query.');
            return;
        }
        try {
            const results = await Location.geocodeAsync(searchQuery);
            if (results.length > 0) {
                const { latitude, longitude } = results[0];
                setRegion({
                    latitude,
                    longitude,
                    latitudeDelta: 0.0922,
                    longitudeDelta: 0.0421,
                });
                setMarker({ latitude, longitude });
                setFields((f) => ({
                    ...f,
                    latitude: latitude.toFixed(6),
                    longitude: longitude.toFixed(6),
                }));
                if (mapRef.current) {
                    mapRef.current.animateToRegion({
                        latitude,
                        longitude,
                        latitudeDelta: 0.0922,
                        longitudeDelta: 0.0421,
                    });
                }
            } else {
                Alert.alert('Error', 'No results found for the search query.');
            }
        } catch (error) {
            console.error('Error searching location:', error);
            Alert.alert('Error', 'Failed to search location.');
        }
    };

    return (
        <View style={styles.inputRow}>
            <Text style={styles.section}>Map</Text>
            <View style={styles.searchContainer}>
                <TextInput
                    style={styles.searchInput}
                    value={searchQuery}
                    onChangeText={setSearchQuery}
                    placeholder="Search Property location..."
                    placeholderTextColor="#999"
                />
                <TouchableOpacity
                    style={styles.searchButton}
                    onPress={handleSearch}
                >
                    <Ionicons name="search" size={20} color="#fff" />
                </TouchableOpacity>
            </View>
            <View style={styles.mapContainer}>
                <MapView
                    ref={mapRef}
                    provider={PROVIDER_GOOGLE}
                    style={styles.map}
                    region={region}
                    onPress={handleMapPress}
                >
                    {marker && (
                        <Marker coordinate={marker} title="Selected Location" />
                    )}
                </MapView>
                <TouchableOpacity
                    style={styles.currentLocationButton}
                    onPress={handleCurrentLocation}
                >
                    <Ionicons name="locate" size={20} color="#fff" />
                </TouchableOpacity>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    inputRow: {
        marginBottom: 16,
    },
    section: {
        fontSize: 18,
        fontWeight: '600',
        color: '#008060',
        marginBottom: 16,
    },
    mapContainer: {
        height: 300,
        borderRadius: 8,
        overflow: 'hidden',
        marginBottom: 16,
        position: 'relative',
    },
    map: {
        ...StyleSheet.absoluteFillObject,
    },
    currentLocationButton: {
        position: 'absolute',
        top: 20,
        right: 16,
        backgroundColor: '#008060',
        borderRadius: 50,
        padding: 7,
        elevation: 5,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.3,
        shadowRadius: 3,
    },
    searchContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 16,
    },
    searchInput: {
        flex: 1,
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        padding: 12,
        fontSize: 16,
        backgroundColor: '#fff',
        color: '#333',
    },
    searchButton: {
        backgroundColor: '#008060',
        borderRadius: 8,
        padding: 12,
        marginLeft: 8,
        alignItems: 'center',
        justifyContent: 'center',
    },
});

export default MapSelector;
