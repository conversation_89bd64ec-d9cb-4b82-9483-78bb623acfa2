import React, { useEffect, useContext } from 'react';
import { View, ActivityIndicator, Text } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useQuery } from '@tanstack/react-query';
import { ThemeContext } from '../../context/ThemeContext';
import { fetchSiteProfileById } from '../../api/sites/sitesApi';
import { showToast } from '../../utils/showToast';
import { styles } from './siteStyles';

const UpdateSite = () => {
    const { theme } = useContext(ThemeContext);
    const router = useRouter();
    const { siteId } = useLocalSearchParams();

    const {
        data: siteData,
        isLoading,
        isError,
        error,
    } = useQuery({
        queryKey: ['siteProfile', siteId],
        queryFn: () => fetchSiteProfileById(siteId),
        enabled: !!siteId,
        onError: (_error) => {
            showToast('error', 'Error', 'Failed to fetch site details.');
        },
    });

    useEffect(() => {
        if (siteData && !isLoading) {
            // Redirect to SiteForm with edit data
            const editData = JSON.stringify(siteData);
            router.replace(
                `/Sites/SiteForm?editData=${encodeURIComponent(editData)}`
            );
        }
    }, [siteData, isLoading, router]);

    if (isLoading) {
        return (
            <View
                style={[
                    styles.container,
                    { backgroundColor: theme.BACKGROUND },
                ]}
            >
                <View style={styles.contentContainer}>
                    <ActivityIndicator size="large" color={theme.PRIMARY} />
                    <Text
                        style={[
                            styles.subtitle,
                            { color: theme.TEXT_PRIMARY, marginTop: 16 },
                        ]}
                    >
                        Loading site details...
                    </Text>
                </View>
            </View>
        );
    }

    if (isError) {
        return (
            <View
                style={[
                    styles.container,
                    { backgroundColor: theme.BACKGROUND },
                ]}
            >
                <View style={styles.contentContainer}>
                    <Text style={[styles.title, { color: theme.ERROR }]}>
                        Error
                    </Text>
                    <Text
                        style={[styles.subtitle, { color: theme.TEXT_PRIMARY }]}
                    >
                        {error?.message || 'Failed to load site details'}
                    </Text>
                </View>
            </View>
        );
    }

    // This should not be reached as we redirect in useEffect
    return null;
};

export default UpdateSite;
