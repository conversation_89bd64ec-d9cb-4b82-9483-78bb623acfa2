import * as Yup from 'yup';

// Common regex patterns for site validation
export const SITE_REGEX_PATTERNS = {
    NAME: /^[a-zA-Z0-9\s.'-]+$/, // Letters, numbers, spaces, dots, apostrophes, hyphens
    PINCODE: /^\d{6}$/, // Exactly 6 digits
    COORDINATE: /^-?\d+(\.\d+)?$/, // Validates latitude/longitude
    SURVEY_NO: /^\d+[A-Z]?(?:\/\d+[A-Z]?)?$/i, // 123, 123/1, or 123/1A
    VILLAGE_DISTRICT: /^[A-Za-z\s.'-]+$/, // Letters, spaces, apostrophes, periods, hyphens
    ENCUMBRANCE_DOC: /^\d{1,6}\/\d{4}$/, // Format: docNo/year
    PROPERTY_TAX:
        /^([A-Z]{0,3}\d{1,3}|[A-Z]{2,5})[/-]\d{1,4}[/-]\d{4}(?:-[0-9]{2})?[/-]\d{3,6}$/i,
};

// File validation constants
export const FILE_CONSTRAINTS = {
    ALLOWED_TYPES: ['image/jpeg', 'image/png', 'application/pdf'],
    MAX_FILE_SIZE: 5 * 1024 * 1024, // 5 MB
    MAX_SITE_IMAGES: 10,
};

// Helper function to create file validation
export const createFileValidation = (fieldName, required = true) => {
    const validation = Yup.object().shape({
        uri: Yup.string().required(),
        name: Yup.string().required(),
        mimeType: Yup.string()
            .oneOf(FILE_CONSTRAINTS.ALLOWED_TYPES, 'Invalid file type')
            .required(),
        size: Yup.number()
            .max(
                FILE_CONSTRAINTS.MAX_FILE_SIZE,
                'File size must be less than 5MB'
            )
            .required(),
    });

    return required
        ? validation.required(`${fieldName} is required`)
        : validation.nullable();
};

// Site Details Step Validation
export const siteDetailsSchema = Yup.object().shape({
    name: Yup.string()
        .trim()
        .required('Site name is required')
        .min(5, 'Site name must be at least 5 characters')
        .max(100, 'Site name must not exceed 100 characters')
        .matches(
            SITE_REGEX_PATTERNS.NAME,
            'Site name can only contain letters, numbers, spaces, dots, apostrophes, and hyphens'
        ),

    addressLine1: Yup.string()
        .trim()
        .required('Address Line 1 is required')
        .min(10, 'Address must be at least 10 characters')
        .max(200, 'Address Line 1 is too long'),

    addressLine2: Yup.string().trim().max(200, 'Address Line 2 is too long'),

    landmark: Yup.string().trim().max(100, 'Landmark is too long'),

    pincode: Yup.string()
        .trim()
        .required('Pincode is required')
        .matches(
            SITE_REGEX_PATTERNS.PINCODE,
            'Pincode must be exactly 6 digits'
        ),

    state: Yup.string()
        .trim()
        .required('State is required')
        .max(100, 'State name is too long')
        .matches(SITE_REGEX_PATTERNS.VILLAGE_DISTRICT, 'Invalid state name'),

    district: Yup.string()
        .trim()
        .required('District is required')
        .max(100, 'District name is too long')
        .matches(SITE_REGEX_PATTERNS.VILLAGE_DISTRICT, 'Invalid district name'),

    plotArea: Yup.number()
        .required('Plot area is required')
        .positive('Plot area must be a positive number')
        .min(1, 'Plot area must be at least 1 sq ft')
        .max(1000000, 'Plot area seems too large'),

    price: Yup.number()
        .required('Price is required')
        .positive('Price must be a positive number')
        .min(1, 'Price must be at least ₹1')
        .max(10000000000, 'Price seems too high'),

    siteImages: Yup.array()
        .min(1, 'At least one site image is required')
        .max(
            FILE_CONSTRAINTS.MAX_SITE_IMAGES,
            `Maximum ${FILE_CONSTRAINTS.MAX_SITE_IMAGES} images allowed`
        )
        .of(createFileValidation('Site image')),
});

// Location Step Validation
export const locationSchema = Yup.object().shape({
    location: Yup.string()
        .trim()
        .required('Location description is required')
        .min(10, 'Location description must be at least 10 characters')
        .max(200, 'Location description is too long'),

    latitude: Yup.string()
        .required('Latitude is required')
        .matches(SITE_REGEX_PATTERNS.COORDINATE, 'Invalid latitude format')
        .test(
            'latitude-range',
            'Latitude must be between -90 and 90',
            function (value) {
                if (!value) return false;
                const lat = parseFloat(value);
                return lat >= -90 && lat <= 90;
            }
        ),

    longitude: Yup.string()
        .required('Longitude is required')
        .matches(SITE_REGEX_PATTERNS.COORDINATE, 'Invalid longitude format')
        .test(
            'longitude-range',
            'Longitude must be between -180 and 180',
            function (value) {
                if (!value) return false;
                const lng = parseFloat(value);
                return lng >= -180 && lng <= 180;
            }
        ),

    village: Yup.string()
        .trim()
        .required('Village is required')
        .max(100, 'Village name is too long')
        .matches(SITE_REGEX_PATTERNS.VILLAGE_DISTRICT, 'Invalid village name'),

    surveyNumber: Yup.string()
        .trim()
        .required('Survey number is required')
        .matches(
            SITE_REGEX_PATTERNS.SURVEY_NO,
            'Invalid survey number format (e.g., 123, 123/1, 123/1A)'
        ),
});

// Encumbrance Step Validation
export const encumbranceSchema = Yup.object().shape({
    encumbranceDocNumber: Yup.string()
        .trim()
        .required('Encumbrance document number is required')
        .matches(
            SITE_REGEX_PATTERNS.ENCUMBRANCE_DOC,
            'Invalid format. Use: documentNumber/year (e.g., 12345/2023)'
        ),

    encumbranceDate: Yup.date()
        .required('Encumbrance date is required')
        .max(new Date(), 'Encumbrance date cannot be in the future'),

    encumbranceCert: createFileValidation('Encumbrance certificate', true),
});

// Property Tax Step Validation
export const propertyTaxSchema = Yup.object().shape({
    propertyTaxNumber: Yup.string()
        .trim()
        .required('Property tax number is required')
        .matches(
            SITE_REGEX_PATTERNS.PROPERTY_TAX,
            'Invalid property tax number format'
        ),

    propertyTaxDate: Yup.date()
        .required('Property tax date is required')
        .max(new Date(), 'Property tax date cannot be in the future'),

    propertyTaxRec: createFileValidation('Property tax receipt', true),
});

// Complete site validation schema (for final submission)
export const completeSiteSchema = Yup.object().shape({
    ...siteDetailsSchema.fields,
    ...locationSchema.fields,
    ...encumbranceSchema.fields,
    ...propertyTaxSchema.fields,
});

// Initial values for the site form
export const initialSiteValues = {
    // Site Details
    name: '',
    addressLine1: '',
    addressLine2: '',
    landmark: '',
    pincode: '',
    state: '',
    district: '',
    plotArea: '',
    price: '',
    siteImages: [],

    // Location Details
    location: '',
    latitude: '',
    longitude: '',
    village: '',
    surveyNumber: '',

    // Encumbrance Details
    encumbranceDocNumber: '',
    encumbranceDate: '',
    encumbranceCert: null,

    // Property Tax Details
    propertyTaxNumber: '',
    propertyTaxDate: '',
    propertyTaxRec: null,
};

// Function to get validation schema based on current step
export const getStepValidationSchema = (step) => {
    switch (step) {
        case 'siteDetails':
            return siteDetailsSchema;
        case 'location':
            return locationSchema;
        case 'encumbrance':
            return encumbranceSchema;
        case 'propertyTax':
            return propertyTaxSchema;
        case 'review':
            return completeSiteSchema;
        default:
            return siteDetailsSchema;
    }
};

// Helper function to validate step data
export const validateStepData = async (step, values) => {
    const schema = getStepValidationSchema(step);
    try {
        await schema.validate(values, { abortEarly: false });
        return { isValid: true, errors: {} };
    } catch (error) {
        const errors = {};
        error.inner.forEach((err) => {
            errors[err.path] = err.message;
        });
        return { isValid: false, errors };
    }
};
